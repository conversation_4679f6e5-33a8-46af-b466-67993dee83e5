import React, { useState, useEffect } from 'react';
import './Navbar.css';

const Navbar = () => {
    return (
        <nav className="navbar">
            <div className="flex justify-between items-center max-w-7xl mx-auto">
                <a href="#" className="navbar-brand">SZ.</a>
                <div className="navbar-menu">
                    <a href="#" className="navbar-link">Home</a>
                    <a href="#" className="navbar-link">My work</a>
                    <a href="#" className="navbar-link">Blog</a>
                    <a href="#" className="navbar-link">More ›</a>
                </div>
                <div className="navbar-settings">⚙</div>
            </div>
        </nav>
    )
}

export default Navbar;