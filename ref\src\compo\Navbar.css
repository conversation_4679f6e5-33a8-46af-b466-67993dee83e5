/* Navbar.css - Simple transparent navbar */

.navbar {
  background-color: transparent;
  padding: 1rem 2rem;
}

.navbar-brand {
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
  text-decoration: none;
}

.navbar-menu {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.navbar-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.2s ease;
  padding: 0.5rem 0;
}

.navbar-link:hover {
  color: white;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
  }

  .navbar-menu {
    gap: 1.5rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }
}
