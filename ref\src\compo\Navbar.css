/* Navbar.css - Custom styles for Navbar component */

.navbar {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
  background: linear-gradient(45deg, #ffffff, #cccccc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  letter-spacing: -0.02em;
}

.navbar-link {
  position: relative;
  transition: all 0.3s ease;
}

.navbar-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(90deg, #ffffff, #cccccc);
  transition: width 0.3s ease;
}

.navbar-link:hover::after {
  width: 100%;
}

.navbar-link:hover {
  transform: translateY(-1px);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .navbar-menu {
    flex-direction: column;
    gap: 1rem;
  }
  
  .navbar-brand {
    font-size: 1.5rem;
  }
}
