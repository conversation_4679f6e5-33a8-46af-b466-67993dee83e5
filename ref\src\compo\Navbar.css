/* Navbar.css - Clean minimal navbar styles */

.navbar {
  background-color: #1a1a1a;
  padding: 1rem 2rem;
  border-bottom: 1px solid #333;
}

.navbar-brand {
  color: white;
  font-weight: 600;
  font-size: 1.2rem;
  text-decoration: none;
  letter-spacing: 0.1em;
}

.navbar-menu {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.navbar-link {
  color: #cccccc;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.2s ease;
  padding: 0.5rem 0;
}

.navbar-link:hover {
  color: white;
}

.navbar-settings {
  color: #cccccc;
  font-size: 1.1rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.navbar-settings:hover {
  color: white;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
  }

  .navbar-menu {
    gap: 1.5rem;
  }

  .navbar-brand {
    font-size: 1.1rem;
  }
}
