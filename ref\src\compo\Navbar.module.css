/* Navbar.module.css - CSS Modules for scoped styling */

.navbar {
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
}

.menu {
  display: flex;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.link {
  color: white;
  text-decoration: none;
  transition: color 0.3s ease;
}

.link:hover {
  color: #cccccc;
}
