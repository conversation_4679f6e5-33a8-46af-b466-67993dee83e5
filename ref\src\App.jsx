import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import Orb from './compo/Orb';
import Navbar from './compo/Navbar';

function App() {
  const [count, setCount] = useState(0)

  return (
    <>
      <Navbar />
      <div style={{ width: '100%', height: '700px', position: 'relative'}}>
        <Orb
          hoverIntensity={1}
          rotateOnHover={true}
          hue={0}
          forceHoverState={false}
        />
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: 'white',
          zIndex: 10,
          pointerEvents: 'none'
        }}>
          <h1 style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            marginBottom: '1rem'
          }}>
            <PERSON><PERSON><PERSON>
          </h1>
        </div>
      </div>
    </>
  )
}

export default App
