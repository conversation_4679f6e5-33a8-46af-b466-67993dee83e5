import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import Orb from './compo/Orb';
import Navbar from './compo/Navbar';
import Squares from './compo/Squares';

function App() {
  const [count, setCount] = useState(0)

  return (
    <>
      <Navbar />
      <div>
        <Squares 
        speed={0.5} 
        squareSize={40}
        direction='diagonal' // up, down, left, right, diagonal
        borderColor='#fff'
        hoverFillColor='#222'
        />
      </div>
      {/* <div style={{ width: '100%', height: '700px', position: 'relative'}}>
        <Orb
          hoverIntensity={1}
          rotateOnHover={true}
          hue={0}
          forceHoverState={false}
        />
        
      </div> */}
    </>
  )
}

export default App
